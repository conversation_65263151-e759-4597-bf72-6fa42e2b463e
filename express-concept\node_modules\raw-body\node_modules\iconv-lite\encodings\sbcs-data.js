"use strict"

// Manually added data to be used by sbcs codec in addition to generated one.

module.exports = {
  // Not supported by iconv, not sure why.
  10029: "maccenteuro",
  maccenteuro: {
    type: "_sbcs",
    chars: "ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ"
  },

  808: "cp808",
  ibm808: "cp808",
  cp808: {
    type: "_sbcs",
    chars: "АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№€■ "
  },

  mik: {
    type: "_sbcs",
    chars: "АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя└┴┬├─┼╣║╚╔╩╦╠═╬┐░▒▓│┤№§╗╝┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ "
  },

  cp720: {
    type: "_sbcs",
    chars: "\x80\x81éâ\x84à\x86çêëèïî\x8d\x8e\x8f\x90\u0651\u0652ô¤ـûùءآأؤ£إئابةتثجحخدذرزسشص«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ضطظعغفµقكلمنهوىي≡\u064b\u064c\u064d\u064e\u064f\u0650≈°∙·√ⁿ²■\u00a0"
  },

  // Aliases of generated encodings.
  ascii8bit: "ascii",
  usascii: "ascii",
  ansix34: "ascii",
  ansix341968: "ascii",
  ansix341986: "ascii",
  csascii: "ascii",
  cp367: "ascii",
  ibm367: "ascii",
  isoir6: "ascii",
  iso646us: "ascii",
  iso646irv: "ascii",
  us: "ascii",

  latin1: "iso88591",
  latin2: "iso88592",
  latin3: "iso88593",
  latin4: "iso88594",
  latin5: "iso88599",
  latin6: "iso885910",
  latin7: "iso885913",
  latin8: "iso885914",
  latin9: "iso885915",
  latin10: "iso885916",

  csisolatin1: "iso88591",
  csisolatin2: "iso88592",
  csisolatin3: "iso88593",
  csisolatin4: "iso88594",
  csisolatincyrillic: "iso88595",
  csisolatinarabic: "iso88596",
  csisolatingreek: "iso88597",
  csisolatinhebrew: "iso88598",
  csisolatin5: "iso88599",
  csisolatin6: "iso885910",

  l1: "iso88591",
  l2: "iso88592",
  l3: "iso88593",
  l4: "iso88594",
  l5: "iso88599",
  l6: "iso885910",
  l7: "iso885913",
  l8: "iso885914",
  l9: "iso885915",
  l10: "iso885916",

  isoir14: "iso646jp",
  isoir57: "iso646cn",
  isoir100: "iso88591",
  isoir101: "iso88592",
  isoir109: "iso88593",
  isoir110: "iso88594",
  isoir144: "iso88595",
  isoir127: "iso88596",
  isoir126: "iso88597",
  isoir138: "iso88598",
  isoir148: "iso88599",
  isoir157: "iso885910",
  isoir166: "tis620",
  isoir179: "iso885913",
  isoir199: "iso885914",
  isoir203: "iso885915",
  isoir226: "iso885916",

  cp819: "iso88591",
  ibm819: "iso88591",

  cyrillic: "iso88595",

  arabic: "iso88596",
  arabic8: "iso88596",
  ecma114: "iso88596",
  asmo708: "iso88596",

  greek: "iso88597",
  greek8: "iso88597",
  ecma118: "iso88597",
  elot928: "iso88597",

  hebrew: "iso88598",
  hebrew8: "iso88598",

  turkish: "iso88599",
  turkish8: "iso88599",

  thai: "iso885911",
  thai8: "iso885911",

  celtic: "iso885914",
  celtic8: "iso885914",
  isoceltic: "iso885914",

  tis6200: "tis620",
  tis62025291: "tis620",
  tis62025330: "tis620",

  10000: "macroman",
  10006: "macgreek",
  10007: "maccyrillic",
  10079: "maciceland",
  10081: "macturkish",

  cspc8codepage437: "cp437",
  cspc775baltic: "cp775",
  cspc850multilingual: "cp850",
  cspcp852: "cp852",
  cspc862latinhebrew: "cp862",
  cpgr: "cp869",

  msee: "cp1250",
  mscyrl: "cp1251",
  msansi: "cp1252",
  msgreek: "cp1253",
  msturk: "cp1254",
  mshebr: "cp1255",
  msarab: "cp1256",
  winbaltrim: "cp1257",

  cp20866: "koi8r",
  20866: "koi8r",
  ibm878: "koi8r",
  cskoi8r: "koi8r",

  cp21866: "koi8u",
  21866: "koi8u",
  ibm1168: "koi8u",

  strk10482002: "rk1048",

  tcvn5712: "tcvn",
  tcvn57121: "tcvn",

  gb198880: "iso646cn",
  cn: "iso646cn",

  csiso14jisc6220ro: "iso646jp",
  jisc62201969ro: "iso646jp",
  jp: "iso646jp",

  cshproman8: "hproman8",
  r8: "hproman8",
  roman8: "hproman8",
  xroman8: "hproman8",
  ibm1051: "hproman8",

  mac: "macintosh",
  csmacintosh: "macintosh"
}
