const urlVersioning = (version) => (req, res, next) => {
     // When mounted on /api/v1, req.originalUrl contains the full path
     // req.path contains only the remaining path after the mount point
     if(req.originalUrl.startsWith(`/api/${version}`) || req.baseUrl === `/api/${version}`){
        next();
     }
     else{
        res.status(404).json({
            status: 'error',
            message: 'API version not found'
        })
     }
}

const headerVersioning = (version) => (req, res, next) => {
    if(req.get('Accept-Version') === version){
        next();
    }else{
        res.status(404).json({
            status: 'error',
            message: 'API version not found'
        })
    }
}
const contentTypeVersioning = (version) => (req, res, next) => {
    const contentType = req.get('Content-Type');
    if(contentType.includes(`application/vnd.api.${version}+json`)){
        next();
    }
    else{
        res.status(404).json({
            status: 'error',
            message: 'API version not found'
        })
    }
}

module.exports = {
    urlVersioning,
    headerVersioning,
    contentTypeVersioning
}