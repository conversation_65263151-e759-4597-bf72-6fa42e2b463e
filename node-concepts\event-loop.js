//timer-> pending callback-> idle, prepare-> poll-> check-> close callbacks

const fs  = require('fs');
const crypto = require('crypto');

console.log("1.script started");
setTimeout(()=>{
  console.log("2.timeout callback(macrotask)");
},0);
setTimeout(()=>{
  console.log("3.timeout callback(macrotask)");
},0);
setImmediate(()=>{
  console.log("4.immediate callback (check ) ");
});
Promise.resolve().then(()=>{
  console.log("5.promise callback (microtask)");
});

process.nextTick(()=>{
    console.log("6.nextTick callback (microtask)");
});
fs.readFile(__filename,()=>{
    console.log("7.file read callback (input/output phase)");
});
crypto.pbkdf2('secret','salt',100000,512,'sha512',(err,key)=>{
   if(err) throw err;
    console.log("8.PBKDF2 operation completed (cpu intensive task)"); 
});
console.log("9.script end");

