//objects -> handeling binary data
//Use Cases
//1.file system operations
//2. cryptography
//3. image processing

const buffer = Buffer.alloc(10);//allocate 10 bytes of memory
console.log(buffer);

const bufferString = Buffer.from('hello');
console.log(bufferString);
const bufferFromArrayOfIntegers = Buffer.from([1,2,3]);
console.log(bufferFromArrayOfIntegers);

buffer.write("Bishow God")
console.log("buffer after write",buffer.toString());

console.log(bufferString[0]);//prints the ascii value of h
console.log(bufferString.slice(0,3));

const newBuffer = Buffer.concat([bufferString,Buffer.from(" world")]);
console.log(newBuffer.toString());

console.log(newBuffer.toJSON());
