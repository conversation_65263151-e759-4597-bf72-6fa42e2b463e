// readable -> use of read() method
//writable -> write() method -> drain event
//duplex -> both readable and writable
//transform -> zlib streams

const fs = require('fs');
const zlib = require('zlib');
const { Transform } = require('stream');
const crypto = require('crypto');

class EncryptStream extends Transform{
    constructor(key,vector){
        super();
        this.key = key;
        this.vector = vector;
    }
    _transform(chunk,encoding,callback){
        const cipher = crypto.createCipheriv('aes-256-cbc',this.key,this.vector);
        const encrypted = Buffer.concat([cipher.update(chunk),cipher.final()]);//encrypt the chunk data
        this.push(encrypted);//push the encrypted data to the readable side
        callback();
    }
}
const key = crypto.randomBytes(32);
const vector = crypto.randomBytes(16);

const read = fs.createReadStream('input.txt');
//new gzip object to compress the stream data
const gzip = zlib.createGzip();

const encryptStream = new EncryptStream(key,vector);
const write = fs.createWriteStream('output.txt.gz.enc');

//read -> gzip -> encrypt -> write
read.pipe(gzip).pipe(encryptStream).pipe(write);
console.log("File compressed and encrypted successfully");
