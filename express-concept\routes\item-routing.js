const express = require('express');
const router = express.Router();
const { asyncHand<PERSON>, APIError }  = require('../middleware/errorHandler');

 const items = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' },
    { id: 3, name: 'Item 3' },
];

router.get('/items', asyncHandler(async (req, res) => {
    res.json({ status: 'success', data: items });
}));
router.post('/items', asyncHandler(async (req, res) => {
        if(!req.body.name){
            throw new APIError('Name is required', 400);
        }
        const newItem = {
            id: items.length + 1,
            name: req.body.name,
        }
        items.push(newItem);
        res.status(201).json({ status: 'success', data: newItem });
}));

module.exports = router;