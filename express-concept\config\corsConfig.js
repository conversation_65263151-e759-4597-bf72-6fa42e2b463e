const cors = require('cors');

const configuration = ()=>{
    return cors({
        //origin -> this will tell that which origin you want user can access your api
        origin:(origin,callback)=>{
            const allowedOrigins = [
                'http://localhost:3000',//local dev
                'https://yourcustomdomain.com'//production domain
            ]
            if(!origin || allowedOrigins.indexOf(origin) !== -1){
                callback(null,true)//giving permission  so that req can be allowed
            }else{
                callback(new Error('Not allowed by CORS'))//not giving permission  so that req can be allowed
            }
        },
        methods:['GET','POST','PUT','DELETE'],
        allowedHeaders:['Content-Type','Authorization','Accept-Version'],
        exposedHeaders :['X-Total-Count','Content-Range'],
        credentials: true, // enable support  for cookies, authorization headers with HTTPS
        preflightContinue: false,// if true, the request will not be aborted before the user function returns
        maxAge:600,//cache the preflight response for 600 seconds -> avoid sending option request multiple times
        optionsSuccessStatus:204//successful status code for the preflight request
    })
}

module.exports = configuration;