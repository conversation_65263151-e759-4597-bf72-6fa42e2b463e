const rateLimit = require("express-rate-limit");

const createBasicRateLimiter = (maxRequest, time) => {
    return rateLimit({
        max: maxRequest,
        windowMs: time,
        message: "Too many requests from this IP, please try again after an hour",
        standardHeaders: true,//return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false,//disable the `X-RateLimit-*` headers
    })
}

module.exports = createBasicRateLimiter;