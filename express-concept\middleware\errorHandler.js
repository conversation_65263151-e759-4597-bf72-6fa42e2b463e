//custom error handler middleware
class APIError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
        this.name = 'APIError';//set the error type to APIError
    }
}
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
}

const globalErrorHandler = (err, req, res, next) => {
    console.log(err.stack);//log the error stack trace for debugging
    if (err instanceof APIError) {
        return res.status(err.statusCode).json({ status: 'error', message: err.message });
    }
    //handel mongoose validation error
    else if (err.name === 'ValidationError') {
        return res.status(400).json({ status: 'error', message: err.message });
    }else{
        return res.status(500).json({ status: 'error', message: 'Internal Server Error' });
    }
}

module.exports = { APIError, asyncHandler, globalErrorHandler };