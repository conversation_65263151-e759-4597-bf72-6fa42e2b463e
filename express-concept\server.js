require('dotenv').config()
const express = require('express')
const cors = require('cors')
const app = express()
const configureCors=require('./config/corsConfig')
const { requestLogger, addTimeStamp } = require('./middleware/customMiddleware')
const {globalErrorHandler} = require('./middleware/errorHandler')
const{urlVersioning} = require('./middleware/apiVersioning')
const createBasicRateLimiter = require('./middleware/rateLimit')
const itemRoutes = require('./routes/item-routing')


//apply rate limiting middleware to all requests
app.use(createBasicRateLimiter(100, 60 * 60 * 1000))//100 requests per hour

app.use(requestLogger)
app.use(addTimeStamp)
app.use(configureCors())
app.use(express.json())
app.use(urlVersioning('v1') )

const port = process.env.PORT || 5000
app.use('/api/v1', itemRoutes)

// Error handling middleware should be last
app.use(globalErrorHandler)
app.listen(port, () => {
    console.log(`server is running on port ${port}`)
})