{"name": "path-to-regexp", "version": "8.3.0", "description": "Express style path to RegExp utility", "keywords": ["express", "regexp", "route", "routing"], "repository": {"type": "git", "url": "https://github.com/pillarjs/path-to-regexp.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}, "license": "MIT", "exports": "./dist/index.js", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist/"], "scripts": {"bench": "vitest bench", "build": "ts-scripts build", "format": "ts-scripts format", "lint": "ts-scripts lint", "prepare": "ts-scripts install && npm run build", "size": "size-limit", "specs": "ts-scripts specs", "test": "ts-scripts test && npm run size"}, "devDependencies": {"@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2", "@types/node": "^22.7.2", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^3.0.5", "recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.7.3", "vitest": "^3.0.5"}, "publishConfig": {"access": "public"}, "size-limit": [{"path": "dist/index.js", "limit": "2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}}